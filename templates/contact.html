<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="聯絡迪奕科技金屬回收服務，提供多種聯絡方式和即時客服支援">
    <meta name="keywords" content="聯絡我們, 客服支援, 金屬回收, 黃金回收, 白銀回收, 聯絡方式">
    <meta name="author" content="迪奕科技">
    <title>聯絡我們 - 迪奕科技金屬回收服務</title>

    <!-- Bulma CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    
    <!-- 自定義樣式 -->
    <style>
        /* 自定義 Bulma 導航樣式 */
        .navbar {
            background-color: #2c3e50 !important;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 99999;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
        }
        
        .navbar-brand .navbar-item {
            font-size: 20px;
            font-weight: 700;
            color: #D4AF37 !important;
        }
        
        .navbar-item {
            color: #ecf0f1 !important;
            font-weight: 500;
        }
        
        .navbar-item:hover,
        .navbar-item.is-active {
            background-color: #D4AF37 !important;
            color: white !important;
        }
        
        .navbar-burger {
            color: #ecf0f1 !important;
            border: 2px solid #ecf0f1;
            border-radius: 4px;
            width: 44px;
            height: 44px;
        }
        
        .navbar-burger span {
            background-color: #ecf0f1 !important;
        }
        
        .navbar-menu {
            background-color: #2c3e50 !important;
            box-shadow: 0 4px 16px rgba(44, 62, 80, 0.2);
        }
        
        @media screen and (max-width: 1023px) {
            .navbar-menu {
                border-top: 1px solid #95a5a6;
            }
            
            .navbar-item {
                padding: 16px 24px;
                font-size: 18px;
            }
        }
        
        /* 主要內容樣式 */
        main {
            margin-top: 3.25rem; /* Bulma navbar 高度 */
            padding: 20px;
        }
        
        .page-header {
            background: linear-gradient(135deg, #D4AF37 0%, #B8860B 100%);
            color: white;
            padding: 40px 20px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 40px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }
        
        .page-header p {
            font-size: 1.2rem;
            margin-bottom: 15px;
        }
        
        .contact-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .contact-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
            margin-bottom: 40px;
        }
        
        .contact-info-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
        }
        
        .contact-info-section h3 {
            color: #2c3e50;
            margin-bottom: 25px;
            padding-bottom: 10px;
            border-bottom: 2px solid #D4AF37;
            font-size: 1.5rem;
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #ecf0f1;
            border-radius: 8px;
            transition: all 0.3s;
        }
        
        .contact-item:hover {
            background: #95a5a6;
            transform: translateX(5px);
        }
        
        .contact-icon {
            font-size: 1.5rem;
            margin-right: 15px;
            color: #D4AF37;
            min-width: 30px;
        }
        
        .contact-details h4 {
            margin: 0 0 5px 0;
            color: #2c3e50;
            font-size: 1.1rem;
        }
        
        .contact-details p {
            margin: 0;
            color: #95a5a6;
            line-height: 1.4;
        }
        
        .contact-details a {
            color: #D4AF37;
            text-decoration: none;
            font-weight: 500;
        }
        
        .contact-details a:hover {
            text-decoration: underline;
        }
        
        .contact-form-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
        }
        
        .contact-form-section h3 {
            color: #2c3e50;
            margin-bottom: 25px;
            padding-bottom: 10px;
            border-bottom: 2px solid #D4AF37;
            font-size: 1.5rem;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #95a5a6;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #D4AF37;
        }
        
        .form-group textarea {
            height: 120px;
            resize: vertical;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #D4AF37 0%, #B8860B 100%);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 6px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(44, 62, 80, 0.2);
        }
        
        .office-hours {
            background: #ecf0f1;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .office-hours h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }
        
        .hours-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .hours-item {
            text-align: center;
            padding: 20px;
            background: white;
            border-radius: 8px;
            border-left: 4px solid #D4AF37;
        }
        
        .hours-item h4 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }
        
        .hours-item p {
            color: #95a5a6;
            margin: 0;
            font-weight: 500;
        }
        
        .map-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
        }
        
        .map-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
            font-size: 1.5rem;
        }
        
        .map-placeholder {
            background: #ecf0f1;
            border: 2px dashed #95a5a6;
            border-radius: 8px;
            padding: 60px 20px;
            text-align: center;
            color: #95a5a6;
        }
        
        .map-placeholder h4 {
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        /* 移動端響應式 */
        @media (max-width: 768px) {
            .contact-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .hours-grid {
                grid-template-columns: 1fr;
            }
        }
        
    </style>
</head>

<body>
    <!-- Bulma 導航選單 -->
    <nav class="navbar" role="navigation" aria-label="main navigation">
        <div class="navbar-brand">
            <a class="navbar-item" href="index.html">
                迪奕科技
            </a>
            
            <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false" data-target="navbarMenu">
                <span aria-hidden="true"></span>
                <span aria-hidden="true"></span>
                <span aria-hidden="true"></span>
            </a>
        </div>
        
        <div id="navbarMenu" class="navbar-menu">
            <div class="navbar-end">
                <a href="index.html" class="navbar-item">首頁</a>
                <a href="products.html" class="navbar-item">產品介紹</a>
                <a href="market-data.html" class="navbar-item">金屬價格</a>
                <a href="booking.html" class="navbar-item">回收預約</a>
                <a href="contact.html" class="navbar-item is-active">聯絡我們</a>
            </div>
        </div>
    </nav>

    <!-- 主要內容 -->
    <main>
        <!-- 頁面標題 -->
        <section class="page-header">
            <h1>聯絡我們</h1>
            <p>我們隨時為您提供專業的金屬回收服務支援</p>
            <p>多種聯絡方式，讓您輕鬆與我們取得聯繫</p>
        </section>

        <div class="contact-container">
            <!-- 聯絡資訊和表單 -->
            <div class="contact-grid">
                <!-- 聯絡資訊 -->
                <section class="contact-info-section">
                    <h3>📞 聯絡資訊</h3>
                    
                    <div class="contact-item">
                        <div class="contact-icon">📱</div>
                        <div class="contact-details">
                            <h4>客服專線</h4>
                            <p><a href="tel:+886212345678">(02) 1234-5678</a></p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">📧</div>
                        <div class="contact-details">
                            <h4>電子郵件</h4>
                            <p><a href="mailto:<EMAIL>"><EMAIL></a></p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">📍</div>
                        <div class="contact-details">
                            <h4>公司地址</h4>
                            <p>台北市信義區金融街123號<br>金融大樓15樓</p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">🕒</div>
                        <div class="contact-details">
                            <h4>服務時間</h4>
                            <p>週一至週五 09:00-18:00<br>週六 09:00-12:00</p>
                        </div>
                    </div>
                    
                    <div class="contact-item">
                        <div class="contact-icon">💬</div>
                        <div class="contact-details">
                            <h4>線上客服</h4>
                            <p>24小時線上諮詢服務<br>專業客服人員即時回應</p>
                        </div>
                    </div>
                </section>

                <!-- 聯絡表單 -->
                <section class="contact-form-section">
                    <h3>✍️ 發送訊息</h3>
                    
                    <form id="contactForm">
                        <div class="form-group">
                            <label for="name">姓名 *</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="email">電子郵件 *</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="phone">聯絡電話</label>
                            <input type="tel" id="phone" name="phone">
                        </div>
                        
                        <div class="form-group">
                            <label for="subject">主旨 *</label>
                            <select id="subject" name="subject" required>
                                <option value="">請選擇主旨</option>
                                <option value="general">一般諮詢</option>
                                <option value="investment">投資相關</option>
                                <option value="insurance">保險相關</option>
                                <option value="retirement">退休規劃</option>
                                <option value="complaint">意見反映</option>
                                <option value="other">其他</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="message">訊息內容 *</label>
                            <textarea id="message" name="message" placeholder="請詳細描述您的問題或需求..." required></textarea>
                        </div>
                        
                        <button type="submit" class="submit-btn">📤 發送訊息</button>
                    </form>
                </section>
            </div>

            <!-- 營業時間 -->
            <section class="office-hours">
                <h3>🕒 詳細營業時間</h3>
                <div class="hours-grid">
                    <div class="hours-item">
                        <h4>週一至週五</h4>
                        <p>09:00 - 18:00</p>
                    </div>
                    <div class="hours-item">
                        <h4>週六</h4>
                        <p>09:00 - 12:00</p>
                    </div>
                    <div class="hours-item">
                        <h4>週日及國定假日</h4>
                        <p>休息</p>
                    </div>
                    <div class="hours-item">
                        <h4>緊急服務</h4>
                        <p>24小時專線</p>
                    </div>
                </div>
            </section>

            <!-- 地圖區域 -->
            <section class="map-section">
                <h3>🗺️ 公司位置</h3>
                <div class="map-placeholder">
                    <h4>📍 台北市信義區金融街123號</h4>
                    <p>金融大樓15樓</p>
                    <p>捷運信義安和站步行5分鐘</p>
                    <p>公車信義幹線、20、37、612等路線</p>
                </div>
            </section>
        </div>
    </main>

    <!-- Bulma 導航 JavaScript -->
    <script>
        // 處理表單提交
        function handleFormSubmit(e) {
            e.preventDefault();
            
            // 獲取表單數據
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);
            
            // 檢查必填欄位
            if (!data.name || !data.email || !data.subject || !data.message) {
                alert('請填寫所有必填欄位');
                return;
            }
            
            // 顯示提交成功訊息
            alert('訊息已成功發送！我們將盡快與您聯繫。');
            
            // 重置表單
            e.target.reset();
        }
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 初始化 Bulma 導航系統...');
            
            // 獲取 Bulma navbar burger
            const navbarBurger = document.querySelector('.navbar-burger');
            const navbarMenu = document.getElementById('navbarMenu');
            
            if (navbarBurger && navbarMenu) {
                // 漢堡選單點擊事件
                navbarBurger.addEventListener('click', function() {
                    console.log('🖱️ Bulma 漢堡選單被點擊');
                    
                    // 切換活動狀態
                    navbarBurger.classList.toggle('is-active');
                    navbarMenu.classList.toggle('is-active');
                    
                    // 更新 aria 屬性
                    const isExpanded = navbarBurger.classList.contains('is-active');
                    navbarBurger.setAttribute('aria-expanded', isExpanded);
                    
                    console.log(isExpanded ? '🔓 導航已開啟' : '🔒 導航已關閉');
                });
                
                // 點擊選單項目時關閉手機版選單
                const navbarItems = navbarMenu.querySelectorAll('.navbar-item');
                navbarItems.forEach(item => {
                    item.addEventListener('click', function() {
                        if (navbarMenu.classList.contains('is-active')) {
                            console.log('🔗 選單項目被點擊，關閉手機版選單');
                            navbarBurger.classList.remove('is-active');
                            navbarMenu.classList.remove('is-active');
                            navbarBurger.setAttribute('aria-expanded', 'false');
                        }
                        
                        // 更新活動狀態
                        navbarItems.forEach(nav => nav.classList.remove('is-active'));
                        this.classList.add('is-active');
                    });
                });
                
                console.log('✅ Bulma 導航系統初始化完成');
            } else {
                console.log('❌ 找不到 Bulma 導航元素');
            }
            
            // 表單提交事件
            const contactForm = document.getElementById('contactForm');
            if (contactForm) {
                contactForm.addEventListener('submit', handleFormSubmit);
                console.log('✅ 聯絡表單事件監聽器已設置');
            }
        });
    </script>
</body>

</html>