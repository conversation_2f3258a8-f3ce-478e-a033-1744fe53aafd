<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="預約專業金屬回收服務，包含黃金、白銀、銅、鋁、不鏽鋼等金屬回收諮詢">
    <meta name="keywords" content="回收預約, 金屬回收, 黃金回收, 白銀回收, 銅回收, 鋁回收, 不鏽鋼回收">
    <meta name="author" content="迪奕科技">
    <title>回收預約 - 迪奕科技金屬回收服務</title>

    <!-- Bulma CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    
    <!-- 自定義樣式 -->
    <style>
        /* 自定義 Bulma 導航樣式 */
        .navbar {
            background-color: #2c3e50 !important;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 99999;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
        }
        
        .navbar-brand .navbar-item {
            font-size: 20px;
            font-weight: 700;
            color: #D4AF37 !important;
        }
        
        .navbar-item {
            color: #ecf0f1 !important;
            font-weight: 500;
        }
        
        .navbar-item:hover,
        .navbar-item.is-active {
            background-color: #D4AF37 !important;
            color: white !important;
        }
        
        .navbar-burger {
            color: #ecf0f1 !important;
            border: 2px solid #ecf0f1;
            border-radius: 4px;
            width: 44px;
            height: 44px;
        }
        
        .navbar-burger span {
            background-color: #ecf0f1 !important;
        }
        
        .navbar-menu {
            background-color: #2c3e50 !important;
            box-shadow: 0 4px 16px rgba(44, 62, 80, 0.2);
        }
        
        @media screen and (max-width: 1023px) {
            .navbar-menu {
                border-top: 1px solid #95a5a6;
            }
            
            .navbar-item {
                padding: 16px 24px;
                font-size: 18px;
            }
        }
        
        /* 主要內容樣式 */
        main {
            margin-top: 3.25rem; /* Bulma navbar 高度 */
            padding: 20px;
        }
        
        .page-header {
            background: linear-gradient(135deg, #D4AF37 0%, #B8860B 100%);
            color: white;
            padding: 40px 20px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 40px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }
        
        .page-header p {
            font-size: 1.2rem;
            margin-bottom: 15px;
        }
        
        .booking-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .booking-form {
            background: white;
            padding: 40px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
            margin-bottom: 30px;
        }
        
        .form-section {
            margin-bottom: 30px;
        }
        
        .form-section h3 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #D4AF37;
        }
        
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group.full-width {
            grid-column: 1 / -1;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            color: #2c3e50;
            font-weight: 500;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #95a5a6;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #D4AF37;
        }
        
        .form-group textarea {
            height: 120px;
            resize: vertical;
        }
        
        .service-options {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .service-option {
            display: flex;
            align-items: center;
            padding: 15px;
            border: 2px solid #95a5a6;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .service-option:hover {
            border-color: #D4AF37;
            background-color: #ecf0f1;
        }
        
        .service-option input[type="checkbox"] {
            margin-right: 10px;
            width: auto;
        }
        
        .service-option label {
            margin: 0;
            cursor: pointer;
            font-weight: normal;
            color: #2c3e50;
        }
        
        .submit-btn {
            background: linear-gradient(135deg, #D4AF37 0%, #B8860B 100%);
            color: white;
            padding: 15px 40px;
            border: none;
            border-radius: 6px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            width: 100%;
        }
        
        .submit-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(44, 62, 80, 0.2);
        }
        
        .contact-info {
            background: #ecf0f1;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
        }
        
        .contact-info h3 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        
        .contact-item {
            margin: 15px 0;
            color: #95a5a6;
        }
        
        .contact-item strong {
            color: #2c3e50;
        }
        
        /* 移動端響應式 */
        @media (max-width: 768px) {
            .form-row {
                grid-template-columns: 1fr;
                gap: 0;
            }
            
            .booking-form {
                padding: 20px;
            }
            
            .service-options {
                grid-template-columns: 1fr;
            }
        }
        
    </style>
</head>

<body>

    <!-- Bulma 導航選單 -->
    <nav class="navbar" role="navigation" aria-label="main navigation">
        <div class="navbar-brand">
            <a class="navbar-item" href="index.html">
                迪奕科技
            </a>
            
            <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false" data-target="navbarMenu">
                <span aria-hidden="true"></span>
                <span aria-hidden="true"></span>
                <span aria-hidden="true"></span>
            </a>
        </div>
        
        <div id="navbarMenu" class="navbar-menu">
            <div class="navbar-end">
                <a href="index.html" class="navbar-item">首頁</a>
                <a href="products.html" class="navbar-item">產品介紹</a>
                <a href="market-data.html" class="navbar-item">金屬價格</a>
                <a href="booking.html" class="navbar-item is-active">回收預約</a>
                <a href="contact.html" class="navbar-item">聯絡我們</a>
            </div>
        </div>
    </nav>

    <!-- 主要內容 -->
    <main>
        <!-- 頁面標題 -->
        <section class="page-header">
            <h1>回收預約</h1>
            <p>專業的金屬回收服務，為您的金屬回收需求提供專業建議</p>
            <p>填寫下方表單，我們將盡快與您聯繫安排回收時間</p>
        </section>

        <div class="booking-container">
            <!-- 預約表單 -->
            <form class="booking-form" id="bookingForm">
                <!-- 基本資料 -->
                <div class="form-section">
                    <h3>📋 基本資料</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="firstName">姓名 *</label>
                            <input type="text" id="firstName" name="firstName" required>
                        </div>
                        <div class="form-group">
                            <label for="lastName">姓氏 *</label>
                            <input type="text" id="lastName" name="lastName" required>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="phone">聯絡電話 *</label>
                            <input type="tel" id="phone" name="phone" required>
                        </div>
                        <div class="form-group">
                            <label for="email">電子郵件 *</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                    </div>
                    
                    <div class="form-group full-width">
                        <label for="address">聯絡地址</label>
                        <input type="text" id="address" name="address">
                    </div>
                </div>

                <!-- 諮詢服務 -->
                <div class="form-section">
                    <h3>🎯 諮詢服務</h3>
                    <div class="service-options">
                        <div class="service-option">
                            <input type="checkbox" id="investment" name="services" value="investment">
                            <label for="investment">投資理財諮詢</label>
                        </div>
                        <div class="service-option">
                            <input type="checkbox" id="insurance" name="services" value="insurance">
                            <label for="insurance">保險規劃諮詢</label>
                        </div>
                        <div class="service-option">
                            <input type="checkbox" id="retirement" name="services" value="retirement">
                            <label for="retirement">退休規劃諮詢</label>
                        </div>
                        <div class="service-option">
                            <input type="checkbox" id="tax" name="services" value="tax">
                            <label for="tax">稅務規劃諮詢</label>
                        </div>
                        <div class="service-option">
                            <input type="checkbox" id="estate" name="services" value="estate">
                            <label for="estate">遺產規劃諮詢</label>
                        </div>
                        <div class="service-option">
                            <input type="checkbox" id="other" name="services" value="other">
                            <label for="other">其他服務</label>
                        </div>
                    </div>
                </div>

                <!-- 預約時間 -->
                <div class="form-section">
                    <h3>📅 預約時間</h3>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="preferredDate">偏好日期 *</label>
                            <input type="date" id="preferredDate" name="preferredDate" required>
                        </div>
                        <div class="form-group">
                            <label for="preferredTime">偏好時段 *</label>
                            <select id="preferredTime" name="preferredTime" required>
                                <option value="">請選擇時段</option>
                                <option value="morning">上午 (09:00-12:00)</option>
                                <option value="afternoon">下午 (13:00-17:00)</option>
                                <option value="evening">晚上 (18:00-21:00)</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-group full-width">
                        <label for="consultationType">諮詢方式</label>
                        <select id="consultationType" name="consultationType">
                            <option value="face-to-face">面對面諮詢</option>
                            <option value="video-call">視訊諮詢</option>
                            <option value="phone">電話諮詢</option>
                        </select>
                    </div>
                </div>

                <!-- 諮詢內容 -->
                <div class="form-section">
                    <h3>💬 諮詢內容</h3>
                    <div class="form-group full-width">
                        <label for="consultationDetails">請簡述您的諮詢需求或問題 *</label>
                        <textarea id="consultationDetails" name="consultationDetails" placeholder="請詳細描述您的財務狀況、投資目標、風險承受度等相關資訊..." required></textarea>
                    </div>
                </div>

                <!-- 提交按鈕 -->
                <button type="submit" class="submit-btn">📤 提交預約申請</button>
            </form>

            <!-- 聯絡資訊 -->
            <section class="contact-info">
                <h3>📞 聯絡資訊</h3>
                <div class="contact-item">
                    <strong>服務時間：</strong> 週一至週五 09:00-18:00
                </div>
                <div class="contact-item">
                    <strong>諮詢專線：</strong> (02) 1234-5678
                </div>
                <div class="contact-item">
                    <strong>電子郵件：</strong> <EMAIL>
                </div>
                <div class="contact-item">
                    <strong>地址：</strong> 台北市信義區金融街123號
                </div>
                <div class="contact-item">
                    <strong>注意事項：</strong> 我們將在收到預約申請後24小時內與您聯繫確認
                </div>
            </section>
        </div>
    </main>

    <!-- Bulma 導航 JavaScript -->
    <script>
        
        // 處理表單提交
        function handleFormSubmit(e) {
            e.preventDefault();
            
            // 獲取表單數據
            const formData = new FormData(e.target);
            const data = Object.fromEntries(formData);
            
            // 檢查必填欄位
            if (!data.firstName || !data.lastName || !data.phone || !data.email || 
                !data.preferredDate || !data.preferredTime || !data.consultationDetails) {
                alert('請填寫所有必填欄位');
                return;
            }
            
            // 檢查是否選擇了服務項目
            const selectedServices = formData.getAll('services');
            if (selectedServices.length === 0) {
                alert('請至少選擇一項諮詢服務');
                return;
            }
            
            // 顯示提交成功訊息
            alert('預約申請已成功提交！我們將在24小時內與您聯繫確認。');
            
            // 重置表單
            e.target.reset();
        }
        
        // 等待DOM完全載入
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 初始化 Bulma 導航和預約表單系統...');
            
            // 初始化 Bulma navbar
            const navbarBurger = document.querySelector('.navbar-burger');
            const navbarMenu = document.getElementById('navbarMenu');
            const bookingForm = document.getElementById('bookingForm');
            
            if (navbarBurger && navbarMenu) {
                // 漢堡選單點擊事件
                navbarBurger.addEventListener('click', function() {
                    console.log('🖱️ Bulma 漢堡選單被點擊');
                    
                    // 切換活動狀態
                    navbarBurger.classList.toggle('is-active');
                    navbarMenu.classList.toggle('is-active');
                    
                    // 更新 aria 屬性
                    const isExpanded = navbarBurger.classList.contains('is-active');
                    navbarBurger.setAttribute('aria-expanded', isExpanded);
                    
                    console.log(isExpanded ? '🔓 導航已開啟' : '🔒 導航已關閉');
                });
                
                // 點擊選單項目時關閉手機版選單
                const navbarItems = navbarMenu.querySelectorAll('.navbar-item');
                navbarItems.forEach(item => {
                    item.addEventListener('click', function() {
                        if (navbarMenu.classList.contains('is-active')) {
                            console.log('🔗 選單項目被點擊，關閉手機版選單');
                            navbarBurger.classList.remove('is-active');
                            navbarMenu.classList.remove('is-active');
                            navbarBurger.setAttribute('aria-expanded', 'false');
                        }
                        
                        // 更新活動狀態
                        navbarItems.forEach(nav => nav.classList.remove('is-active'));
                        this.classList.add('is-active');
                    });
                });
                
                console.log('✅ Bulma 導航系統初始化完成');
            } else {
                console.log('❌ 找不到 Bulma 導航元素');
            }
            
            // 表單提交事件
            if (bookingForm) {
                bookingForm.addEventListener('submit', handleFormSubmit);
                console.log('✅ 預約表單事件監聽器已設置');
            }
            
            console.log('✅ 預約服務頁面系統初始化完成');
        });
    </script>
</body>

</html>