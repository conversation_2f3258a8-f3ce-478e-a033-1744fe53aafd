<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>金屬價格管理系統</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <style>
        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .header {
            background: linear-gradient(135deg, #D4AF37, #B8860B);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
        }
        
        .price-card {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            margin-bottom: 1rem;
            border-left: 4px solid #D4AF37;
        }
        
        .price-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: #D4AF37;
        }
        
        .form-container {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 2rem;
        }
        
        .btn-gold {
            background-color: #D4AF37;
            border-color: #D4AF37;
            color: white;
        }
        
        .btn-gold:hover {
            background-color: #B8860B;
            border-color: #B8860B;
            color: white;
        }
        
        .alert {
            border-radius: 8px;
        }
        
        .form-label {
            font-weight: 600;
            color: #495057;
        }
        
        .form-control:focus {
            border-color: #D4AF37;
            box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
        }
        
        .update-time {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .login-container {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #D4AF37, #B8860B);
        }

        .login-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            padding: 2rem;
            width: 100%;
            max-width: 400px;
        }

        .login-header {
            text-align: center;
            margin-bottom: 2rem;
            color: #D4AF37;
        }

        .token-input {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 12px;
            font-size: 1rem;
        }

        .token-input:focus {
            border-color: #D4AF37;
            box-shadow: 0 0 0 0.2rem rgba(212, 175, 55, 0.25);
        }
    </style>
</head>
<body>
    <!-- 登入界面 -->
    <div id="loginContainer" class="login-container" style="display: none;">
        <div class="login-card">
            <div class="login-header">
                <h3><i class="fas fa-lock me-2"></i>管理員認證</h3>
                <p class="text-muted">請輸入管理員 Token 以訪問價格管理系統</p>
            </div>

            <form id="loginForm">
                <div class="mb-3">
                    <label for="adminToken" class="form-label">管理員 Token</label>
                    <input type="password" class="form-control token-input" id="adminToken"
                           placeholder="請輸入您的管理員 Token" required>
                </div>
                <div class="d-grid">
                    <button type="submit" class="btn btn-gold btn-lg">
                        <i class="fas fa-sign-in-alt me-2"></i>登入
                    </button>
                </div>
            </form>

            <div id="loginError" class="alert alert-danger mt-3" style="display: none;"></div>

            <div class="mt-4 text-center">
                <small class="text-muted">
                    <i class="fas fa-info-circle me-1"></i>
                    Token 可在服務器啟動日誌或 .env 文件中找到
                </small>
            </div>
        </div>
    </div>

    <!-- 管理介面 -->
    <div id="adminContainer" style="display: none;">
    <!-- 標題區域 -->
    <div class="header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <a href="/" title="回到首頁" style="color: #fff;text-decoration: none;"><h1><i class="fas fa-coins me-3"></i>金屬價格管理系統</h1></a>
                    <p class="mb-0">Flask API 價格更新管理介面</p>
                </div>
                <div class="col-md-4 text-end">
                    <button class="btn btn-outline-light btn-sm me-3" onclick="adminAuth.logout()">
                        <i class="fas fa-sign-out-alt me-1"></i>登出
                    </button>
                    <div class="update-time">
                        <i class="fas fa-clock me-1"></i>
                        最後更新：<span id="lastUpdateTime">載入中...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container">
        <!-- 消息提示 -->
        <div id="messageContainer"></div>

        <div class="row">
            <!-- 當前價格顯示 -->
            <div class="col-lg-6">
                <h3><i class="fas fa-chart-line me-2"></i>當前價格</h3>
                
                <div class="price-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5><i class="fas fa-medal text-warning me-2"></i>黃金</h5>
                            <div class="price-value" id="goldPrice">載入中...</div>
                        </div>
                        <i class="fas fa-coins fa-2x text-warning"></i>
                    </div>
                </div>

                <div class="price-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5><i class="fas fa-medal text-secondary me-2"></i>白銀</h5>
                            <div class="price-value" id="silverPrice">載入中...</div>
                        </div>
                        <i class="fas fa-coins fa-2x text-secondary"></i>
                    </div>
                </div>

                <div class="price-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5><i class="fas fa-medal text-danger me-2"></i>銅</h5>
                            <div class="price-value" id="copperPrice">載入中...</div>
                        </div>
                        <i class="fas fa-industry fa-2x text-danger"></i>
                    </div>
                </div>

                <div class="price-card">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5><i class="fas fa-medal text-info me-2"></i>鋁</h5>
                            <div class="price-value" id="aluminumPrice">載入中...</div>
                        </div>
                        <i class="fas fa-industry fa-2x text-info"></i>
                    </div>
                </div>

                <div class="mt-3">
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        更新者：<span id="updatedBy">載入中...</span> |
                        來源：<span id="priceSource">載入中...</span>
                    </small>
                </div>
            </div>

            <!-- 價格更新表單 -->
            <div class="col-lg-6">
                <h3><i class="fas fa-edit me-2"></i>更新價格</h3>
                
                <div class="form-container">
                    <form id="priceUpdateForm">
                        <div class="mb-3">
                            <label for="goldInput" class="form-label">黃金價格 (元/公克)</label>
                            <input type="number" class="form-control" id="goldInput"
                                   step="0.01" min="0.01" max="100000" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="mb-3">
                            <label for="silverInput" class="form-label">白銀價格 (元/公克)</label>
                            <input type="number" class="form-control" id="silverInput"
                                   step="0.01" min="0.01" max="10000" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="mb-3">
                            <label for="copperInput" class="form-label">銅價格 (元/公斤)</label>
                            <input type="number" class="form-control" id="copperInput"
                                   step="0.01" min="0.01" max="10000" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="mb-3">
                            <label for="aluminumInput" class="form-label">鋁價格 (元/公斤)</label>
                            <input type="number" class="form-control" id="aluminumInput"
                                   step="0.01" min="0.01" max="10000" required>
                            <div class="invalid-feedback"></div>
                        </div>

                        <div class="mb-3">
                            <label for="updatedByInput" class="form-label">更新者</label>
                            <input type="text" class="form-control" id="updatedByInput"
                                   maxlength="50" value="管理員">
                        </div>

                        <div class="d-grid">
                            <button type="submit" class="btn btn-gold btn-lg">
                                <i class="fas fa-save me-2"></i>更新價格
                            </button>
                        </div>
                    </form>
                </div>

                <!-- API 信息 -->
                <div class="mt-4">
                    <h5><i class="fas fa-code me-2"></i>API 端點</h5>
                    <div class="card">
                        <div class="card-body">
                            <p class="card-text">
                                <strong>獲取價格：</strong><br>
                                <code>GET /api/prices</code>
                            </p>
                            <p class="card-text">
                                <strong>更新價格：</strong><br>
                                <code>POST /api/prices</code>
                            </p>
                            <p class="card-text">
                                <strong>健康檢查：</strong><br>
                                <code>GET /health</code>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        class AdminAuth {
            constructor() {
                this.tokenKey = 'admin_token';
                this.currentPrices = null;
                this.init();
            }

            init() {
                // 頁面載入時檢查認證狀態
                this.checkAuthStatus();

                // 綁定登入表單事件
                document.getElementById('loginForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handleLogin();
                });

                // 綁定價格更新表單事件
                document.getElementById('priceUpdateForm').addEventListener('submit', (e) => {
                    e.preventDefault();
                    this.handlePriceUpdate();
                });
            }

            async checkAuthStatus() {
                const token = localStorage.getItem(this.tokenKey);

                if (token) {
                    // 驗證 token 是否仍然有效
                    const isValid = await this.validateToken(token);
                    if (isValid) {
                        await this.showAdminPanel();
                        return;
                    } else {
                        // token 無效，清除並顯示登入頁面
                        localStorage.removeItem(this.tokenKey);
                    }
                }

                this.showLoginForm();
            }

            async validateToken(token) {
                try {
                    const response = await fetch('/api/validate-token', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ token: token })
                    });

                    return response.ok;
                } catch (error) {
                    console.error('Token 驗證失敗:', error);
                    return false;
                }
            }

            async handleLogin() {
                const token = document.getElementById('adminToken').value.trim();

                if (!token) {
                    this.showError('請輸入 Token');
                    return;
                }

                try {
                    const response = await fetch('/api/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ token: token })
                    });

                    const result = await response.json();

                    if (result.success) {
                        // 登入成功，保存 token 並顯示管理面板
                        localStorage.setItem(this.tokenKey, token);
                        await this.showAdminPanel();
                        this.hideError();
                    } else {
                        this.showError(result.message || '登入失敗');
                    }
                } catch (error) {
                    console.error('登入請求失敗:', error);
                    this.showError('網絡錯誤，請重試');
                }
            }

            async loadCurrentPrices() {
                const token = localStorage.getItem(this.tokenKey);

                try {
                    const response = await fetch('/api/admin/current-prices', {
                        method: 'GET',
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    if (response.ok) {
                        this.currentPrices = await response.json();
                        this.updatePriceDisplay();
                        this.fillPriceForm();
                    } else {
                        this.showMessage('載入價格失敗', 'danger');
                    }
                } catch (error) {
                    console.error('載入價格失敗:', error);
                    this.showMessage('載入價格失敗', 'danger');
                }
            }

            updatePriceDisplay() {
                if (!this.currentPrices) return;

                document.getElementById('goldPrice').textContent =
                    this.formatPrice(this.currentPrices.gold) + ' 元/公克';
                document.getElementById('silverPrice').textContent =
                    this.formatPrice(this.currentPrices.silver) + ' 元/公克';
                document.getElementById('copperPrice').textContent =
                    this.formatPrice(this.currentPrices.copper) + ' 元/公斤';
                document.getElementById('aluminumPrice').textContent =
                    this.formatPrice(this.currentPrices.aluminum) + ' 元/公斤';

                document.getElementById('lastUpdateTime').textContent =
                    this.currentPrices.updateTime || '未知';
                document.getElementById('updatedBy').textContent =
                    this.currentPrices.updatedBy || '未知';
                document.getElementById('priceSource').textContent =
                    this.currentPrices.source || '未知';
            }

            fillPriceForm() {
                if (!this.currentPrices) return;

                document.getElementById('goldInput').value = this.currentPrices.gold || '';
                document.getElementById('silverInput').value = this.currentPrices.silver || '';
                document.getElementById('copperInput').value = this.currentPrices.copper || '';
                document.getElementById('aluminumInput').value = this.currentPrices.aluminum || '';
            }

            formatPrice(price) {
                if (typeof price !== 'number') return '載入中...';
                return new Intl.NumberFormat('zh-TW', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                }).format(price);
            }

            async handlePriceUpdate() {
                const token = localStorage.getItem(this.tokenKey);

                const priceData = {
                    gold: parseFloat(document.getElementById('goldInput').value),
                    silver: parseFloat(document.getElementById('silverInput').value),
                    copper: parseFloat(document.getElementById('copperInput').value),
                    aluminum: parseFloat(document.getElementById('aluminumInput').value),
                    updatedBy: document.getElementById('updatedByInput').value || '管理員'
                };

                try {
                    const response = await fetch('/api/admin/update-prices', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${token}`
                        },
                        body: JSON.stringify(priceData)
                    });

                    const result = await response.json();

                    if (result.success) {
                        this.showMessage('價格更新成功！', 'success');
                        await this.loadCurrentPrices(); // 重新載入價格
                    } else {
                        this.showMessage(result.error || '更新失敗', 'danger');
                    }
                } catch (error) {
                    console.error('價格更新失敗:', error);
                    this.showMessage('網絡錯誤，請重試', 'danger');
                }
            }

            async showAdminPanel() {
                document.getElementById('loginContainer').style.display = 'none';
                document.getElementById('adminContainer').style.display = 'block';
                await this.loadCurrentPrices();
            }

            showLoginForm() {
                document.getElementById('loginContainer').style.display = 'block';
                document.getElementById('adminContainer').style.display = 'none';
                document.getElementById('adminToken').focus();
            }

            showError(message) {
                const errorDiv = document.getElementById('loginError');
                errorDiv.textContent = message;
                errorDiv.style.display = 'block';
            }

            hideError() {
                document.getElementById('loginError').style.display = 'none';
            }

            showMessage(message, type = 'info') {
                const messageContainer = document.getElementById('messageContainer');
                const alertClass = type === 'success' ? 'alert-success' :
                                 type === 'danger' ? 'alert-danger' : 'alert-info';

                const alertHtml = `
                    <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                        <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;

                messageContainer.innerHTML = alertHtml;

                // 自動隱藏成功消息
                if (type === 'success') {
                    setTimeout(() => {
                        const alert = messageContainer.querySelector('.alert');
                        if (alert) {
                            const bsAlert = new bootstrap.Alert(alert);
                            bsAlert.close();
                        }
                    }, 3000);
                }
            }

            // 提供登出功能
            logout() {
                localStorage.removeItem(this.tokenKey);
                this.showLoginForm();
            }
        }

        // 頁面載入完成後初始化認證系統
        document.addEventListener('DOMContentLoaded', function() {
            window.adminAuth = new AdminAuth();
        });
    </script>
</body>
</html>
