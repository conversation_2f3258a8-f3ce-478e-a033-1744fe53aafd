<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="最新金屬市場價格與分析，包含黃金、白銀、銅、鋁、不鏽鋼等金屬價格資訊">
    <meta name="keywords" content="金屬價格, 黃金價格, 白銀價格, 銅價, 鋁價, 不鏽鋼價格, 金屬回收">
    <meta name="author" content="迪奕科技">
    <title>金屬價格 - 迪奕科技金屬回收服務</title>

    <!-- Bulma CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    
    <!-- 自定義樣式 -->
    <style>
        /* 自定義 Bulma 導航樣式 */
        .navbar {
            background-color: #2c3e50 !important;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 99999;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
        }
        
        .navbar-brand .navbar-item {
            font-size: 20px;
            font-weight: 700;
            color: #D4AF37 !important;
        }
        
        .navbar-item {
            color: #ecf0f1 !important;
            font-weight: 500;
        }
        
        .navbar-item:hover,
        .navbar-item.is-active {
            background-color: #D4AF37 !important;
            color: white !important;
        }
        
        .navbar-burger {
            color: #ecf0f1 !important;
            border: 2px solid #ecf0f1;
            border-radius: 4px;
            width: 44px;
            height: 44px;
        }
        
        .navbar-burger span {
            background-color: #ecf0f1 !important;
        }
        
        .navbar-menu {
            background-color: #2c3e50 !important;
            box-shadow: 0 4px 16px rgba(44, 62, 80, 0.2);
        }
        
        @media screen and (max-width: 1023px) {
            .navbar-menu {
                border-top: 1px solid #95a5a6;
            }
            
            .navbar-item {
                padding: 16px 24px;
                font-size: 18px;
            }
        }
        
        /* 主要內容樣式 */
        main {
            margin-top: 3.25rem; /* Bulma navbar 高度 */
            padding: 20px;
        }
        
        .page-header {
            background: linear-gradient(135deg, #D4AF37 0%, #B8860B 100%);
            color: white;
            padding: 40px 20px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 40px;
        }
        
        .page-header h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }
        
        .page-header p {
            font-size: 1.2rem;
            margin-bottom: 15px;
        }
        
        .market-overview {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
            margin-bottom: 30px;
        }
        
        .market-overview h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .market-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .market-card {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #D4AF37;
        }
        
        .market-card h3 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 1.2rem;
        }
        
        .market-value {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
            color: #2c3e50;
        }
        
        .market-change {
            font-size: 0.9rem;
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-block;
        }
        
        .market-change.positive {
            background-color: #d4edda;
            color: #155724;
        }
        
        .market-change.negative {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .market-change.neutral {
            background-color: #e2e3e5;
            color: #383d41;
        }
        
        .chart-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
            margin-bottom: 30px;
        }
        
        .chart-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .chart-placeholder {
            background: #ecf0f1;
            border: 2px dashed #95a5a6;
            border-radius: 8px;
            padding: 60px 20px;
            text-align: center;
            color: #95a5a6;
        }
        
        .chart-placeholder h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .news-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
        }
        
        .news-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .news-item {
            padding: 15px 0;
            border-bottom: 1px solid #ecf0f1;
        }
        
        .news-item:last-child {
            border-bottom: none;
        }
        
        .news-title {
            color: #2c3e50;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .news-summary {
            color: #95a5a6;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .news-time {
            color: #95a5a6;
            font-size: 0.8rem;
            margin-top: 5px;
        }
        
    </style>
    <style>
    .tv-overview-container {
        padding: 56.25% 0 0 0;
        position: relative;
    }

    /* 手機端樣式：螢幕寬度小於 768px 時套用 */
    @media (max-width: 768px) {
        .tv-overview-container {
            padding: 120% 0 0 0;
        }
    }
    </style>
</head>

<body>
    <!-- Bulma 導航選單 -->
    <nav class="navbar" role="navigation" aria-label="main navigation">
        <div class="navbar-brand">
            <a class="navbar-item" href="index.html">
                迪奕科技
            </a>
            
            <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false" data-target="navbarMenu">
                <span aria-hidden="true"></span>
                <span aria-hidden="true"></span>
                <span aria-hidden="true"></span>
            </a>
        </div>
        
        <div id="navbarMenu" class="navbar-menu">
            <div class="navbar-end">
                <a href="index.html" class="navbar-item">首頁</a>
                <a href="products.html" class="navbar-item">產品介紹</a>
                <a href="market-data.html" class="navbar-item is-active">金屬價格</a>
                <a href="booking.html" class="navbar-item">回收預約</a>
                <a href="contact.html" class="navbar-item">聯絡我們</a>
            </div>
        </div>
    </nav>

    <!-- 主要內容 -->
    <main>
        <!-- 頁面標題 -->
        <section class="page-header">
            <h1>金屬價格</h1>
            <p>最新金屬市場價格與專業分析</p>
            <p>掌握金屬市場脈動，了解回收價格趨勢</p>
        </section>

        <!-- 市場概覽 -->
        <section class="market-overview">
            <h2>市場概覽</h2>
            <div class="market-grid">
                <div class="market-card">
                    <h3>台股指數</h3>
                    <div class="market-value">17,850.21</div>
                    <div class="market-change positive">+125.43 (+0.71%)</div>
                </div>
                
                <div class="market-card">
                    <h3>美元指數</h3>
                    <div class="market-value">102.45</div>
                    <div class="market-change negative">-0.32 (-0.31%)</div>
                </div>
                
                <div class="market-card">
                    <h3>黃金價格</h3>
                    <div class="market-value" id="gold-price">載入中...</div>
                    <div class="market-change neutral">元/公克</div>
                </div>

                <div class="market-card">
                    <h3>白銀價格</h3>
                    <div class="market-value" id="silver-price">載入中...</div>
                    <div class="market-change neutral">元/公克</div>
                </div>

                <div class="market-card">
                    <h3>銅價格</h3>
                    <div class="market-value" id="copper-price">載入中...</div>
                    <div class="market-change neutral">元/公斤</div>
                </div>
                
                <div class="market-card">
                    <h3>鋁價格</h3>
                    <div class="market-value" id="aluminum-price">載入中...</div>
                    <div class="market-change neutral">元/公斤</div>
                </div>
                
                <div class="market-card">
                    <h3>原油價格</h3>
                    <div class="market-value">$78.25</div>
                    <div class="market-change neutral">$0.00 (0.00%)</div>
                </div>
            </div>

            <!-- 價格更新時間 -->
            <div style="text-align: center; margin-top: 20px; color: #95a5a6; font-size: 0.9rem;">
                <i class="fas fa-clock"></i> <span id="price-update-time">價格更新時間：載入中...</span>
            </div>
        </section>

        <!-- 圖表區域 -->
        <section class="chart-section">
            <h2>市場走勢圖</h2>
            <h6 style="font-size: 70%;
    text-align: center;">以下報價由TradingView提供，價格可能有延遲或者錯誤的可能，僅供參考</h6>
            <!-- TradingView Widget BEGIN -->
            <div class="tv-overview-container"></div>
            <script>
                // 等待頁面載入完成
                document.addEventListener('DOMContentLoaded', function() {
                    initTradingViewWidget();
                });
                
                function initTradingViewWidget() {
                    try {
                        // 結構化配置
                        const widgetConfig = {
                            "colorTheme": "light",
                            "dateRange": "1D",
                            "showChart": true,
                            "width": "100%",
                            "height": "100%",
                            "largeChartUrl": "",
                            "isTransparent": false,
                            "showSymbolLogo": false,
                            "showFloatingTooltip": true,
                            "plotLineColorGrowing": "rgba(195, 166, 96, 1.0)",
                            "plotLineColorFalling": "rgba(195, 166, 96, 1.0)",
                            "gridLineColor": "rgba(226, 210, 163, 1.0)",
                            "scaleFontColor": "rgba(142, 124, 80, 1.0)",
                            "belowLineFillColorGrowing": "rgba(249, 239, 219, 1.0)",
                            "belowLineFillColorFalling": "rgba(249, 239, 219, 1.0)",
                            "belowLineFillColorGrowingBottom": "rgba(255, 255, 255, 1.0)",
                            "belowLineFillColorFallingBottom": "rgba(255, 255, 255, 1.0)",
                            "symbolActiveColor": "rgba(249, 239, 219, 1.0)",
                            "tabs": [
                                {
                                    "title": "黃金",
                                    "symbols": [
                                        {"s": "XAUUSD", "d": "黃金(盎司) / 美元"},
                                        {"s": "XAUUSD*USDTWD", "d": "黃金(盎司) / 台幣"},
                                        {"s": "XAUUSD*USDTWD/30.2785*3.75", "d": "黃金(台錢) / 台幣"},
                                        {"s": "XAUUSD / TVC:USOIL", "d": "黃金 / 原油"}
                                    ]
                                },
                                {
                                    "title": "白銀",
                                    "symbols": [
                                        {"s": "XAGUSD", "d": "白銀(盎司) / 美元"},
                                        {"s": "XAGUSD*USDTWD", "d": "白銀(盎司) / 台幣"},
                                        {"s": "XAGUSD*USDTWD/30.2785*3.75", "d": "白銀(台錢) / 台幣"}
                                    ]
                                },
                                {
                                    "title": "鉑金",
                                    "symbols": [
                                        {"s": "XPTUSD", "d": "鉑金(盎司) / 美元"},
                                        {"s": "XPTUSD*USDTWD", "d": "鉑金(盎司) / 台幣"},
                                        {"s": "XPTUSD*USDTWD/30.2785*3.75", "d": "鉑金(台錢) / 台幣"}
                                    ]
                                },
                                {
                                    "title": "鈀金",
                                    "symbols": [
                                        {"s": "XPDUSD", "d": "鈀金(盎司) / 美元"},
                                        {"s": "XPDUSD*USDTWD", "d": "鈀金(盎司) / 台幣"},
                                        {"s": "XPDUSD*USDTWD/30.2785*3.75", "d": "鈀金(台錢) / 台幣"}
                                    ]
                                }
                            ],
                            "utm_source": "widget_demo",
                            "utm_medium": "widget",
                            "utm_campaign": "market-overview"
                        };
                        
                        // 編碼配置為 JSON 字符串
                        const configString = JSON.stringify(widgetConfig);
                        const encodedConfig = encodeURIComponent(configString);
                        
                        // 構建完整的 iframe src
                        const baseUrl = "https://www.tradingview-widget.com/embed-widget/market-overview/";
                        const src = `${baseUrl}?locale=zh_TW#${encodedConfig}`;
                        
                        console.log('Widget config:', widgetConfig);
                        console.log('Generated URL:', src);
                        
                        // 創建 iframe
                        const iframe = document.createElement("iframe");
                        iframe.src = src;
                        iframe.width = "100%";
                        iframe.height = "100%";
                        iframe.frameBorder = "0";
                        iframe.scrolling = "no";
                        iframe.allowTransparency = "true";
                        iframe.className = "tv-widget-market-overview";
                        iframe.title = "TradingView 貴金屬價格圖表";
                        iframe.style.cssText = "position:absolute;top:0;left:0;height: 100%; width: 100%;";
                        
                        // 載入事件處理
                        iframe.onload = function() {
                            console.log('TradingView widget loaded successfully');
                            const loadingDiv = document.querySelector('.loading');
                            if (loadingDiv) {
                                loadingDiv.style.display = 'none';
                            }
                        };
                        
                        iframe.onerror = function() {
                            console.error('TradingView widget failed to load');
                            showError('圖表載入失敗，請稍後再試');
                        };
                        
                        // 清空容器並添加 iframe
                        const container = document.querySelector(".tv-overview-container");
                        container.innerHTML = '<div class="loading">載入中...</div>';
                        container.appendChild(iframe);
                        
                        // 設置超時檢查
                        setTimeout(function() {
                            const loadingDiv = document.querySelector('.loading');
                            if (loadingDiv && loadingDiv.style.display !== 'none') {
                                showError('圖表載入超時，請檢查網絡連接或稍後再試');
                            }
                        }, 15000);
                        
                    } catch (error) {
                        console.error('Error initializing TradingView widget:', error);
                        showError('初始化圖表時發生錯誤: ' + error.message);
                    }
                }
                
                function showError(message) {
                    const container = document.querySelector(".tv-overview-container");
                    container.innerHTML = `
                        <div class="error-message">
                            <h3>❌ 載入錯誤</h3>
                            <p>${message}</p>
                            <button onclick="initTradingViewWidget()" style="margin-top: 10px; padding: 8px 16px; background: #3498db; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                重新載入
                            </button>
                        </div>
                    `;
                }
                
                // 添加錯誤處理
                window.addEventListener('error', function(e) {
                    console.error('Global error:', e.error);
                });
            </script>
            <!-- TradingView Widget END -->
        </section>

        <!-- 市場新聞 -->
        <section class="news-section">
            <h2>最新市場動態</h2>
            <div class="news-item">
                <div class="news-title">台股開盤上漲，科技股領漲</div>
                <div class="news-summary">今日台股開盤表現強勁，科技股在AI概念股帶動下全面上漲，台積電、聯發科等權值股表現亮眼。</div>
                <div class="news-time">2024年1月16日 09:30</div>
            </div>
            
            <div class="news-item">
                <div class="news-title">美聯儲官員釋放鴿派信號</div>
                <div class="news-summary">多位美聯儲官員表示，通膨壓力正在緩解，市場預期明年可能開始降息，美元指數應聲下跌。</div>
                <div class="news-time">2024年1月16日 08:45</div>
            </div>
            
            <div class="news-item">
                <div class="news-title">黃金避險需求上升</div>
                <div class="news-summary">地緣政治緊張局勢持續，投資者避險情緒升溫，黃金價格突破2,040美元關卡，創下近期新高。</div>
                <div class="news-time">2024年1月16日 08:15</div>
            </div>
            
            <div class="news-item">
                <div class="news-title">原油供應緊張緩解</div>
                <div class="news-summary">OPEC+減產協議執行率下降，加上美國頁岩油產量增加，原油價格維持在78美元附近震盪。</div>
                <div class="news-time">2024年1月16日 07:30</div>
            </div>
        </section>
    </main>

    <!-- Bulma 導航 JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 初始化 Bulma 導航系統...');
            
            // 獲取 Bulma navbar burger
            const navbarBurger = document.querySelector('.navbar-burger');
            const navbarMenu = document.getElementById('navbarMenu');
            
            if (navbarBurger && navbarMenu) {
                // 漢堡選單點擊事件
                navbarBurger.addEventListener('click', function() {
                    console.log('🖱️ Bulma 漢堡選單被點擊');
                    
                    // 切換活動狀態
                    navbarBurger.classList.toggle('is-active');
                    navbarMenu.classList.toggle('is-active');
                    
                    // 更新 aria 屬性
                    const isExpanded = navbarBurger.classList.contains('is-active');
                    navbarBurger.setAttribute('aria-expanded', isExpanded);
                    
                    console.log(isExpanded ? '🔓 導航已開啟' : '🔒 導航已關閉');
                });
                
                // 點擊選單項目時關閉手機版選單
                const navbarItems = navbarMenu.querySelectorAll('.navbar-item');
                navbarItems.forEach(item => {
                    item.addEventListener('click', function() {
                        if (navbarMenu.classList.contains('is-active')) {
                            console.log('🔗 選單項目被點擊，關閉手機版選單');
                            navbarBurger.classList.remove('is-active');
                            navbarMenu.classList.remove('is-active');
                            navbarBurger.setAttribute('aria-expanded', 'false');
                        }
                        
                        // 更新活動狀態
                        navbarItems.forEach(nav => nav.classList.remove('is-active'));
                        this.classList.add('is-active');
                    });
                });
                
                console.log('✅ Bulma 導航系統初始化完成');
            } else {
                console.log('❌ 找不到 Bulma 導航元素');
            }
        });
    </script>

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Font Awesome for icons -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    
    <!-- 圖表功能 -->
    <script src="/static/js/chart.js"></script>

    <!-- 新的價格讀取器 - 使用Unix時間戳緩存 -->
    <script src="/static/js/price-reader.js"></script>
</body>

</html>