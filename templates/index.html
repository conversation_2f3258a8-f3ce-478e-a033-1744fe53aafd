<!DOCTYPE html>
<html lang="zh-TW">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="專業金屬回收服務網站，提供貴金屬回收、市場價格、線上回收預約">
    <meta name="keywords" content="金屬回收, 黃金回收, 白銀回收, 銅回收, 鋁回收, 不鏽鋼回收">
    <meta name="author" content="迪奕科技">
    <title>首頁 - 迪奕科技金屬回收服務</title>

    <!-- Bulma CSS -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bulma@0.9.4/css/bulma.min.css">
    
    <!-- 自定義樣式 -->
    <style>
        /* 自定義 Bulma 導航樣式 */
        .navbar {
            background-color: #2c3e50 !important;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 99999;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
        }
        
        .navbar-brand .navbar-item {
            font-size: 20px;
            font-weight: 700;
            color: #D4AF37 !important;
        }
        
        .navbar-item {
            color: #ecf0f1 !important;
            font-weight: 500;
        }
        
        .navbar-item:hover,
        .navbar-item.is-active {
            background-color: #D4AF37 !important;
            color: white !important;
        }
        
        .navbar-burger {
            color: #ecf0f1 !important;
            border: 2px solid #ecf0f1;
            border-radius: 4px;
            width: 44px;
            height: 44px;
        }
        
        .navbar-burger span {
            background-color: #ecf0f1 !important;
        }
        
        .navbar-menu {
            background-color: #2c3e50 !important;
            box-shadow: 0 4px 16px rgba(44, 62, 80, 0.2);
        }
        
        @media screen and (max-width: 1023px) {
            .navbar-menu {
                border-top: 1px solid #95a5a6;
            }
            
            .navbar-item {
                padding: 16px 24px;
                font-size: 18px;
            }
        }
        
        /* 主要內容樣式 */
        main {
            margin-top: 3.25rem; /* Bulma navbar 高度 */
            padding: 20px;
        }
        
        .hero {
            background: linear-gradient(135deg, #D4AF37 0%, #B8860B 100%);
            color: white;
            padding: 60px 20px;
            text-align: center;
            border-radius: 10px;
            margin-bottom: 40px;
        }
        
        .hero h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
        }
        
        .hero p {
            font-size: 1.2rem;
            margin-bottom: 15px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 10px;
            text-decoration: none;
            border-radius: 5px;
            font-weight: bold;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background-color: #2c3e50;
            color: white;
        }
        
        .btn-outline {
            background-color: transparent;
            color: white;
            border: 2px solid white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            color: white;
            box-shadow: 0 4px 8px rgba(44, 62, 80, 0.2);
        }
        
        .features {
            padding: 40px 0;
        }
        
        .feature-card {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(44, 62, 80, 0.1);
            margin-bottom: 30px;
            text-align: center;
        }
        
        .feature-icon {
            font-size: 3rem;
            color: #D4AF37;
            margin-bottom: 20px;
        }
        
        .feature-content h4 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .feature-content p {
            color: #95a5a6;
            line-height: 1.6;
        }
        

    </style>
</head>

<body>


    <!-- Bulma 導航選單 -->
    <nav class="navbar" role="navigation" aria-label="main navigation">
        <div class="navbar-brand">
            <a class="navbar-item" href="index.html">
                迪奕科技
            </a>
            
            <a role="button" class="navbar-burger" aria-label="menu" aria-expanded="false" data-target="navbarMenu">
                <span aria-hidden="true"></span>
                <span aria-hidden="true"></span>
                <span aria-hidden="true"></span>
            </a>
        </div>
        
        <div id="navbarMenu" class="navbar-menu">
            <div class="navbar-end">
                <a href="index.html" class="navbar-item is-active">首頁</a>
                <a href="products.html" class="navbar-item">產品介紹</a>
                <a href="market-data.html" class="navbar-item">金屬價格</a>
                <a href="booking.html" class="navbar-item">回收預約</a>
                <a href="contact.html" class="navbar-item">聯絡我們</a>
            </div>
        </div>
    </nav>

    <!-- 主要內容 -->
    <main>
        <!-- Hero Section -->
        <section class="hero">
            <h1>專業金屬回收服務</h1>
            <p>提供優質的貴金屬回收與專業諮詢服務</p>
            <p>憑藉多年市場經驗，為您提供最優惠的回收價格</p>
            <div class="hero-buttons">
                <a href="booking.html" class="btn btn-primary">立即預約回收</a>
                <a href="products.html" class="btn btn-outline">瀏覽回收項目</a>
            </div>
        </section>

        <!-- 服務特色 -->
        <section class="features">
            <div class="feature-card">
                <div class="feature-icon">🥇</div>
                <div class="feature-content">
                    <h4>黃金回收</h4>
                    <p>專業鑑定黃金純度，提供最優惠的回收價格，包含金飾、金條、金幣等各種黃金製品。</p>
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🥈</div>
                <div class="feature-content">
                    <h4>白銀回收</h4>
                    <p>高價回收各類白銀製品，從銀飾到銀餐具，專業評估，誠信交易。</p>
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">🔧</div>
                <div class="feature-content">
                    <h4>工業金屬回收</h4>
                    <p>回收銅、鋁、不鏽鋼等工業金屬，為企業提供環保的廢料處理解決方案。</p>
                </div>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <div class="feature-content">
                    <h4>即時價格查詢</h4>
                    <p>提供最新的金屬市場價格資訊，讓您掌握最佳回收時機，獲得最大收益。</p>
                </div>
            </div>
        </section>
    </main>

    <!-- 價格讀取器 -->
    <script src="/static/js/price-reader.js"></script>
    
    <!-- Bulma 導航 JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 初始化 Bulma 導航系統...');
            
            // 獲取 Bulma navbar burger
            const navbarBurger = document.querySelector('.navbar-burger');
            const navbarMenu = document.getElementById('navbarMenu');
            
            if (navbarBurger && navbarMenu) {
                // 漢堡選單點擊事件
                navbarBurger.addEventListener('click', function() {
                    console.log('🖱️ Bulma 漢堡選單被點擊');
                    
                    // 切換活動狀態
                    navbarBurger.classList.toggle('is-active');
                    navbarMenu.classList.toggle('is-active');
                    
                    // 更新 aria 屬性
                    const isExpanded = navbarBurger.classList.contains('is-active');
                    navbarBurger.setAttribute('aria-expanded', isExpanded);
                    
                    console.log(isExpanded ? '🔓 導航已開啟' : '🔒 導航已關閉');
                });
                
                // 點擊選單項目時關閉手機版選單
                const navbarItems = navbarMenu.querySelectorAll('.navbar-item');
                navbarItems.forEach(item => {
                    item.addEventListener('click', function() {
                        if (navbarMenu.classList.contains('is-active')) {
                            console.log('🔗 選單項目被點擊，關閉手機版選單');
                            navbarBurger.classList.remove('is-active');
                            navbarMenu.classList.remove('is-active');
                            navbarBurger.setAttribute('aria-expanded', 'false');
                        }
                        
                        // 更新活動狀態
                        navbarItems.forEach(nav => nav.classList.remove('is-active'));
                        this.classList.add('is-active');
                    });
                });
                
                console.log('✅ Bulma 導航系統初始化完成');
            } else {
                console.log('❌ 找不到 Bulma 導航元素');
            }
        });
    </script>
</body>

</html>
