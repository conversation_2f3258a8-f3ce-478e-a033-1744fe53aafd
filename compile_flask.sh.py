import os
import sys
import subprocess
import logging
import io

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 獲取當前腳本所在的目錄，假設腳本位於專案根目錄
CURRENT_SCRIPT_PATH = os.path.abspath(__file__)
PROJECT_ROOT = os.path.dirname(CURRENT_SCRIPT_PATH)

# 主要應用程式檔案
MAIN_APP = "app.py"
# 輸出的二進制檔名稱
OUTPUT_NAME = "gold_xy"

def prompt_user(message, default_yes=True):
    """向使用者顯示提示訊息，並根據輸入返回布林值。"""
    suffix = "[Y/n]" if default_yes else "[y/N]"
    while True:
        try:
            response = input(f"{message} {suffix}: ").strip().lower()
            if response == 'y' or (response == '' and default_yes):
                return True
            elif response == 'n' or (response == '' and not default_yes):
                return False
            else:
                print("請輸入 'y' 或 'n'。")
        except EOFError:
            return default_yes
        except KeyboardInterrupt:
            print("\n操作已取消。")
            sys.exit(1)

def check_nuitka_installed():
    """檢查 Nuitka 是否已安裝，並在未安裝時詢問使用者是否安裝。"""
    try:
        # 使用 sys.executable 確保調用的是當前環境的 python
        subprocess.run([sys.executable, "-m", "nuitka", "--version"], check=True, capture_output=True)
        logger.info("Nuitka 已安裝。")
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.warning("Nuitka 未找到。")
        if prompt_user("Nuitka 未安裝。是否嘗試安裝 Nuitka？", default_yes=True):
            try:
                logger.info("正在安裝 Nuitka...")
                subprocess.run([sys.executable, "-m", "pip", "install", "-U", "nuitka"], check=True)
                logger.info("Nuitka 安裝成功！")
                return True
            except Exception as e:
                logger.error(f"安裝 Nuitka 失敗: {e}")
                return False
        else:
            logger.error("Nuitka 未安裝且使用者選擇不安裝。無法繼續。")
            return False

def get_packages_from_requirements(req_file="requirements.txt"):
    """從 requirements.txt 讀取套件名稱列表，並轉換為正確的模組名稱。"""
    # 套件名稱映射：requirements.txt 中的名稱 -> 實際的 Python 模組名稱
    package_mapping = {
        'Flask': 'flask',
        'Flask-CORS': 'flask_cors',
        'Flask-WTF': 'flask_wtf',
        'WTForms': 'wtforms',
        'Werkzeug': 'werkzeug',
        'nuitka': None  # 跳過 nuitka 本身
    }

    packages = []
    try:
        with open(req_file, "r") as f:
            for line in f:
                # 簡單解析：忽略註釋、空白行、版本指定符
                package_name = line.strip().split('#')[0].split('==')[0].split('>=')[0].split('<')[0].strip()
                # 確保解析出的 package_name 不是空字串，也不是開頭為 '#' 的註釋行
                if package_name and not package_name.startswith('#'):
                    # 使用映射轉換包名稱
                    mapped_name = package_mapping.get(package_name, package_name.lower())
                    if mapped_name:  # 跳過 None 值（如 nuitka）
                        packages.append(mapped_name)
    except FileNotFoundError:
        logger.warning(f"未找到 {req_file} 檔案。")
    except Exception as e:
        logger.error(f"讀取 {req_file} 時發生錯誤: {e}")
    return packages

def build_executable():
    """使用 Nuitka 編譯 Flask 應用程式，並根據 requirements.txt 動態包含套件。"""
    logger.info("開始編譯 Flask 應用程式...")
    logger.info(f"專案根目錄: {PROJECT_ROOT}")

    # 獲取 requirements.txt 中的套件列表
    packages_to_include_args = []
    requirements = get_packages_from_requirements()
    if requirements:
        logger.info(f"從 requirements.txt 找到的套件: {', '.join(requirements)}")
        for pkg in requirements:
            packages_to_include_args.append(f"--include-package={pkg}")
    else:
        logger.warning("未找到或無法解析 requirements.txt 中的套件。將依賴 Nuitka 的自動分析。")

    original_dir = os.getcwd()
    try:
        os.chdir(PROJECT_ROOT)
        logger.info(f"已切換工作目錄至: {os.getcwd()}")

        # 確保使用虛擬環境中的 Python
        venv_python = "/home/<USER>/下載/gold-main/.venv/bin/python3.11"
        if os.path.exists(venv_python):
            python_executable = venv_python
            logger.info(f"使用虛擬環境 Python: {python_executable}")
        else:
            python_executable = sys.executable
            logger.warning(f"虛擬環境 Python 不存在，使用系統 Python: {python_executable}")

        # 建構 Nuitka 的命令 - 使用虛擬環境
        nuitka_command = [
            python_executable, "-m", "nuitka",
            "--standalone",
            # "--onefile",  # 已移除 --onefile 選項，以解決讀取執行檔同目錄.env 的問題
            f"--output-filename={OUTPUT_NAME}",
            "--include-data-dir=templates=templates",
            "--include-data-dir=static=static",
            MAIN_APP
        ]

        # 完全依賴 Nuitka 的自動分析，不手動指定任何包
        logger.info("使用 Nuitka 自動依賴分析，添加兼容性選項")

        # 詢問用戶是否啟用優化選項
        # if prompt_user("是否啟用 Nuitka 的優化選項 (--strip, --lto=yes)？這可能會增加編譯時間但可能減小文件大小", default_yes=True):
        #     # 由於 Nuitka 版本不支援直接傳遞這些選項，我們暫時移除它們
        #     # nuitka_command.append("--strip")
        #     # nuitka_command.append("--lto=yes")
        #     logger.info("已啟用優化選項: --strip, --lto=yes")

        # 如果 Nuitka 2.7.11 仍然報錯 'flask' 插件，則可能需要手動包含 Flask 及其子模組
        # 但目前我們移除 plugins，依賴自動分析和 --include-package (如果有的話)

        logger.info(f"執行編譯命令: {' '.join(nuitka_command)}")

        # 執行編譯命令
        process = subprocess.run(nuitka_command, check=True, text=True)

        print("\n" + "="*50)
        print("編譯成功！")
        # 修改此處以正確反映輸出的目錄結構
        print(f"輸出的二進制檔位於: {os.path.join(PROJECT_ROOT, 'app.dist', OUTPUT_NAME)}")
        print("="*50 + "\n")

        # --- 運行編譯後的二進制檔進行依賴檢查 ---
        if prompt_user("編譯完成。是否要運行一次應用程式進行簡單測試？", default_yes=True):
            logger.info("正在運行編譯後的應用程式進行測試...")
            # 修改 run_command 以指向包含目錄中的執行檔
            run_command = [os.path.join(PROJECT_ROOT, 'app.dist', OUTPUT_NAME)]
            try:
                # 設置較短的超時時間，避免長時間等待
                run_process = subprocess.run(run_command, check=False, capture_output=True, text=True, cwd=PROJECT_ROOT, timeout=10)
                if run_process.returncode != 0:
                    logger.warning(f"運行測試時返回非零碼: {run_process.returncode}")
                    logger.warning(f"測試運行 stdout:\n{run_process.stdout}")
                    logger.error(f"測試運行 stderr:\n{run_process.stderr}")

                    # 檢查是否是 SIGABRT (134) 錯誤
                    if run_process.returncode == 134:
                        logger.error("執行檔遇到 SIGABRT 錯誤，可能是編譯配置問題")
                        logger.error("建議檢查：1) 系統缺少必要的庫 2) Nuitka 版本兼容性 3) Python 環境問題")

                    if "ModuleNotFoundError" in run_process.stderr or "ImportError" in run_process.stderr:
                        logger.error("偵測到缺失的模組。")
                        if prompt_user("偵測到缺失的套件。是否嘗試從 requirements.txt 安裝？", default_yes=True):
                            logger.info("正在嘗試從 requirements.txt 安裝依賴項...")
                            try:
                                subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
                                logger.info("依賴項安裝完成。建議您重新運行編譯腳本。")
                            except Exception as install_e:
                                logger.error(f"安裝依賴項失敗: {install_e}")
                        else:
                            logger.warning("使用者選擇不安裝缺失的依賴項。")
                    else:
                        logger.error("運行測試時發生其他錯誤。")
                else:
                    logger.info("測試運行成功。")
                    logger.info(f"測試運行 stdout:\n{run_process.stdout}")

            except subprocess.TimeoutExpired:
                logger.warning("測試運行超時，可能應用程式正在正常啟動中...")
                logger.info("請手動測試執行檔是否正常工作")
            except FileNotFoundError:
                logger.error(f"無法找到編譯後的執行檔: {os.path.join(PROJECT_ROOT, 'app.dist', OUTPUT_NAME)}")
            except Exception as run_e:
                logger.error(f"運行編譯後的應用程式時發生錯誤: {run_e}")

    except FileNotFoundError:
        logger.error("錯誤：找不到 'python' 命令。請確保 Python 已正確安裝並在 PATH 中。")
        sys.exit(1)
    except subprocess.CalledProcessError as e:
        logger.error(f"Nuitka 編譯過程中發生錯誤！錯誤代碼: {e.returncode}")
        logger.error(f"執行命令: {' '.join(e.cmd)}")
        logger.error(f"stdout:\n{e.stdout}")
        logger.error(f"stderr:\n{e.stderr}")
        logger.warning("\n編譯失敗。您可能需要檢查：")
        logger.warning("1. Nuitka 的日誌輸出，看是否有具體的套件遺漏或編譯錯誤。")
        logger.warning("2. 您的 requirements.txt 是否列出了所有必需的套件。")
        logger.warning("3. 您可能需要手動為某些套件指定 --include-package 選項。")
        logger.warning("4. 作業系統可能缺少靜態庫 sudo apt install python3-dev 、sudo apt install patchelf 安裝在debian。")
        if prompt_user("編譯失敗，是否嘗試從 requirements.txt 安裝依賴項？", default_yes=False):
            logger.info("正在嘗試從 requirements.txt 安裝依賴項...")
            try:
                subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], check=True)
                logger.info("依賴項安裝完成。請重新運行此編譯腳本。")
            except Exception as install_e:
                logger.error(f"安裝依賴項失敗: {install_e}")
        else:
            logger.warning("使用者選擇不解析 requirements.txt。")

    except Exception as e:
        logger.error(f"處理編譯腳本時發生未知錯誤: {e}")
        sys.exit(1)
    finally:
        os.chdir(original_dir)
        logger.info(f"已恢復工作目錄至: {os.getcwd()}")

if __name__ == "__main__":
    if check_nuitka_installed():
        build_executable()