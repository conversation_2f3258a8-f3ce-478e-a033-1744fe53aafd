// 新的價格讀取器 - 使用Unix時間戳緩存機制
class PriceReader {
    constructor() {
        // Flask API 設定 - 使用相對路徑（同域名）
        this.apiBaseUrl = ''; // 相對路徑，自動使用當前域名
        
        this.defaultPrices = {
            gold: 2045.80,
            silver: 24.50,
            copper: 185.00,
            aluminum: 45.00
        };
        
        this.lastPrices = null;
        this.init();
    }

    // 初始化
    init() {
        console.log('🚀 初始化價格讀取器...');
        this.loadPrices();
        
        // 每5分鐘檢查一次價格更新（可以根據需要調整）
        setInterval(() => {
            this.loadPrices();
        }, 5 * 60 * 1000); // 5分鐘
    }

    // 計算15分鐘緩存時間戳
    getCacheTimestamp() {
        // 獲取當前 Unix 時間戳（秒）
        const nowSeconds = Math.floor(Date.now() / 1000);
        
        // 15分鐘 = 900秒
        const cacheInterval = 15 * 60; // 900秒
        
        // 計算當前時間所屬的15分鐘區間起始點
        const cacheTimestamp = Math.floor(nowSeconds / cacheInterval) * cacheInterval;
        
        console.log(`⏰ 當前時間: ${nowSeconds}, 緩存時間戳: ${cacheTimestamp}`);
        return cacheTimestamp;
    }

    // 從 Flask API 載入價格
    async loadPrices() {
        try {
            console.log('📊 正在載入最新價格...');
            
            // 獲取緩存時間戳
            const cacheTimestamp = this.getCacheTimestamp();
            
            // 構建 API URL，加上緩存時間戳
            const apiUrl = `${this.apiBaseUrl}/api/prices?t=${cacheTimestamp}`;
            
            console.log(`🔗 API URL: ${apiUrl}`);
            
            // 發送請求到 Flask API
            const response = await fetch(apiUrl, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache'
                },
                // 不使用瀏覽器緩存，讓我們的時間戳緩存機制來控制
                cache: 'no-cache'
            });

            if (response.ok) {
                const priceData = await response.json();
                console.log('✅ 從 Flask API 載入價格成功:', priceData);
                
                this.lastPrices = priceData;
                this.updatePriceDisplay(priceData);
                this.showUpdateNotification(priceData);
                
            } else {
                console.warn(`⚠️ Flask API 響應錯誤: ${response.status}`);
                throw new Error(`API 響應錯誤: ${response.status}`);
            }
            
        } catch (error) {
            console.error('❌ 載入價格失敗:', error);
            
            // 如果 API 失敗，使用預設價格
            console.log('🔄 使用預設價格作為備用');
            const fallbackData = {
                ...this.defaultPrices,
                lastUpdate: new Date().toISOString(),
                updateTime: new Date().toLocaleString('zh-TW', { timeZone: 'Asia/Taipei' }),
                updatedBy: '預設價格',
                source: 'Fallback Data',
                note: 'API 不可用，使用預設價格'
            };
            
            this.updatePriceDisplay(fallbackData);
        }
    }

    // 更新頁面上的價格顯示
    updatePriceDisplay(priceData) {
        try {
            // 更新各金屬價格
            const goldElement = document.getElementById('gold-price');
            const silverElement = document.getElementById('silver-price');
            const copperElement = document.getElementById('copper-price');
            const aluminumElement = document.getElementById('aluminum-price');
            const updateTimeElement = document.getElementById('price-update-time');

            if (goldElement) {
                goldElement.textContent = this.formatPrice(priceData.gold);
            }
            if (silverElement) {
                silverElement.textContent = this.formatPrice(priceData.silver);
            }
            if (copperElement) {
                copperElement.textContent = this.formatPrice(priceData.copper);
            }
            if (aluminumElement) {
                aluminumElement.textContent = this.formatPrice(priceData.aluminum);
            }

            // 更新時間顯示
            if (updateTimeElement) {
                const updateTime = priceData.updateTime || priceData.lastUpdate || '未知';
                updateTimeElement.textContent = `價格更新時間：${updateTime}`;
            }

            console.log('✅ 價格顯示更新完成');
            
        } catch (error) {
            console.error('❌ 更新價格顯示失敗:', error);
        }
    }

    // 格式化價格顯示
    formatPrice(price) {
        if (typeof price !== 'number') {
            return '載入中...';
        }
        
        // 格式化為千分位顯示
        return new Intl.NumberFormat('zh-TW', {
            minimumFractionDigits: 0,
            maximumFractionDigits: 2
        }).format(price);
    }

    // 顯示更新通知
    showUpdateNotification(priceData) {
        // 如果有更新通知區域，可以在這裡顯示
        if (priceData.source) {
            console.log(`📡 價格來源: ${priceData.source}`);
        }
        
        if (priceData.note) {
            console.log(`📝 備註: ${priceData.note}`);
        }
    }

    // 手動刷新價格（可以綁定到按鈕）
    async refreshPrices() {
        console.log('🔄 手動刷新價格...');
        await this.loadPrices();
    }

    // 獲取當前價格數據
    getCurrentPrices() {
        return this.lastPrices;
    }
}

// 全域變數，方便其他腳本使用
let priceReader;

// 頁面載入完成後初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🌐 頁面載入完成，初始化價格讀取器...');
    priceReader = new PriceReader();
});

// 提供全域函數供其他腳本調用
window.refreshPrices = function() {
    if (priceReader) {
        priceReader.refreshPrices();
    }
};

window.getCurrentPrices = function() {
    if (priceReader) {
        return priceReader.getCurrentPrices();
    }
    return null;
};
