#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask API 應用 - 金屬價格管理系統
提供價格更新和查詢API，支持表單驗證和CORS
整合所有功能到單一文件，支持跨平台部署
"""

from flask import Flask, request, jsonify, render_template, redirect, url_for, flash
from flask_cors import CORS
from flask_wtf import FlaskForm
from wtforms import FloatField, SubmitField, StringField
from wtforms.validators import DataRequired, NumberRange, Length
from datetime import datetime
import json
import os
import sys
import logging
import random
import string
import stat

# 設置日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 獲取可執行檔的路徑（包括 exe 或 py）
executable_path = sys.executable

# 如果是打包成 exe 後的可執行檔，則取得該檔案所在目錄
if getattr(sys, 'frozen', False):
    executable_dir = os.path.dirname(executable_path)
else:
    # 如果是直接運行 .py 檔案，則取得該檔案所在目錄
    executable_dir = os.path.dirname(os.path.abspath(__file__))

def generate_secure_token(length=32):
    """生成安全的英文+數字 token"""
    characters = string.ascii_letters + string.digits  # a-z, A-Z, 0-9
    return ''.join(random.choice(characters) for _ in range(length))

def init_env_file():
    """初始化 .env 文件"""
    env_path = os.path.join(executable_dir, ".env")

    if not os.path.exists(env_path):
        print("🔐 .env 文件不存在，正在生成...")

        # 生成安全的 tokens
        flask_secret = generate_secure_token(64)  # Flask SECRET_KEY
        admin_token = generate_secure_token(32)   # 管理員 token

        env_content = f"""# Flask 應用配置 - 自動生成
# 請妥善保管此文件，不要提交到版本控制系統

# Flask 應用密鑰（用於 session 和 CSRF）
FLASK_SECRET_KEY={flask_secret}

# 管理員認證 token
ADMIN_TOKEN={admin_token}

# 其他配置
FLASK_ENV=production
FLASK_HOST=0.0.0.0
FLASK_PORT=5000
"""

        try:
            with open(env_path, 'w', encoding='utf-8') as f:
                f.write(env_content)

            # 設置文件權限（僅所有者可讀寫）
            os.chmod(env_path, stat.S_IRUSR | stat.S_IWUSR)

            print(f"✅ .env 文件已生成")
            return admin_token
        except Exception:
            return None
    else:
        return None

def load_env_file():
    """讀取 .env 文件"""
    env_path = os.path.join(executable_dir, ".env")

    if os.path.exists(env_path):
        try:
            with open(env_path, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            return True
        except Exception:
            return False
    return False

# 初始化 .env 文件
new_admin_token = init_env_file()
load_env_file()

# 創建Flask應用
app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('FLASK_SECRET_KEY', 'fallback-secret-key')

# 啟用CORS支持
CORS(app, origins=['http://localhost:3000', 'http://localhost:8000', 'http://127.0.0.1:3000', 'http://127.0.0.1:8000'])

# 價格數據文件路徑 - 使用跨平台路徑
DATA_DIR = os.path.join(executable_dir,"static", "data")
PRICES_FILE = os.path.join(DATA_DIR, "prices.json")

# 確保數據目錄存在
os.makedirs(DATA_DIR, exist_ok=True)

# 移除詳細日誌輸出

# 預設價格數據
DEFAULT_PRICES = {
    "gold": 2045.80,
    "silver": 24.50,
    "copper": 185.00,
    "aluminum": 45.00,
    "lastUpdate": datetime.now().isoformat(),
    "updateTime": datetime.now().strftime('%Y年%m月%d日 %H:%M:%S'),
    "updatedBy": "系統預設",
    "source": "Flask API"
}

class PriceUpdateForm(FlaskForm):
    """價格更新表單"""
    gold = FloatField('黃金價格 (元/公克)', 
                     validators=[DataRequired(message='請輸入黃金價格'), 
                               NumberRange(min=0.01, max=100000, message='價格必須在0.01-100000之間')])
    
    silver = FloatField('白銀價格 (元/公克)', 
                       validators=[DataRequired(message='請輸入白銀價格'), 
                                 NumberRange(min=0.01, max=10000, message='價格必須在0.01-10000之間')])
    
    copper = FloatField('銅價格 (元/公斤)', 
                       validators=[DataRequired(message='請輸入銅價格'), 
                                 NumberRange(min=0.01, max=10000, message='價格必須在0.01-10000之間')])
    
    aluminum = FloatField('鋁價格 (元/公斤)', 
                         validators=[DataRequired(message='請輸入鋁價格'), 
                                   NumberRange(min=0.01, max=10000, message='價格必須在0.01-10000之間')])
    
    updated_by = StringField('更新者', 
                           validators=[Length(max=50, message='更新者名稱不能超過50個字符')],
                           default='管理員')
    
    submit = SubmitField('更新價格')

def load_prices():
    """載入價格數據"""
    try:
        if os.path.exists(PRICES_FILE):
            with open(PRICES_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                logger.info(f"成功載入價格數據: {data}")
                return data
        else:
            logger.warning("價格文件不存在，使用預設價格")
            return DEFAULT_PRICES.copy()
    except Exception as e:
        logger.error(f"載入價格數據失敗: {e}")
        return DEFAULT_PRICES.copy()

def save_prices(prices_data):
    """保存價格數據"""
    try:
        with open(PRICES_FILE, 'w', encoding='utf-8') as f:
            json.dump(prices_data, f, ensure_ascii=False, indent=2)
        logger.info(f"成功保存價格數據: {prices_data}")
        return True
    except Exception as e:
        logger.error(f"保存價格數據失敗: {e}")
        return False

@app.route('/')
def index():
    """首頁 - 靜態網站首頁"""
    return render_template('index.html')

@app.route('/products')
def products():
    """產品介紹頁面"""
    return render_template('products.html')

@app.route('/market-data')
def market_data():
    """金屬價格頁面"""
    return render_template('market-data.html')

@app.route('/booking')
def booking():
    """回收預約頁面"""
    return render_template('booking.html')

@app.route('/contact')
def contact():
    """聯絡我們頁面"""
    return render_template('contact.html')

@app.route('/admin-secure')
def admin_secure():
    """管理介面 - 需要認證的價格管理"""
    return render_template('admin_secure.html')

@app.route('/admin-secure.html')
def admin_secure_html():
    """兼容原始 admin-secure.html 路徑"""
    return render_template('admin_secure.html')

# 兼容 .html 後綴的路由
@app.route('/index.html')
def index_html():
    """兼容原始 index.html 路徑"""
    return render_template('index.html')

@app.route('/products.html')
def products_html():
    """兼容原始 products.html 路徑"""
    return render_template('products.html')

@app.route('/market-data.html')
def market_data_html():
    """兼容原始 market-data.html 路徑"""
    return render_template('market-data.html')

@app.route('/booking.html')
def booking_html():
    """兼容原始 booking.html 路徑"""
    return render_template('booking.html')

@app.route('/contact.html')
def contact_html():
    """兼容原始 contact.html 路徑"""
    return render_template('contact.html')

@app.route('/api/login', methods=['POST'])
def admin_login():
    """管理員登入驗證"""
    data = request.get_json()
    token = data.get('token', '').strip()
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if token and token == admin_token:
        return jsonify({'success': True, 'message': '驗證成功'})
    else:
        return jsonify({'success': False, 'message': '無效的 Token'}), 401

@app.route('/api/validate-token', methods=['POST'])
def validate_token():
    """驗證 token 有效性"""
    data = request.get_json()
    token = data.get('token', '').strip()
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if token and token == admin_token:
        return jsonify({'valid': True})
    else:
        return jsonify({'valid': False}), 401

@app.route('/api/admin/current-prices', methods=['GET'])
def get_admin_prices():
    """獲取當前價格（需要認證）"""
    # 檢查 token
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': '需要認證'}), 401

    token = auth_header.split(' ')[1]
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if token != admin_token:
        return jsonify({'error': '無效的 Token'}), 401

    # 返回價格數據
    current_prices = load_prices()
    return jsonify(current_prices)

@app.route('/api/admin/update-prices', methods=['POST'])
def update_prices():
    """更新價格 - API 方式（需要認證）"""
    # 檢查 token
    auth_header = request.headers.get('Authorization')
    if not auth_header or not auth_header.startswith('Bearer '):
        return jsonify({'error': '需要認證'}), 401

    token = auth_header.split(' ')[1]
    admin_token = os.environ.get('ADMIN_TOKEN', '')

    if token != admin_token:
        return jsonify({'error': '無效的 Token'}), 401

    # 獲取數據
    data = request.get_json()

    try:
        # 驗證數據
        required_fields = ['gold', 'silver', 'copper', 'aluminum']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必要字段: {field}'}), 400

            try:
                float(data[field])
            except (ValueError, TypeError):
                return jsonify({'error': f'字段 {field} 必須是有效數字'}), 400

        # 構建新的價格數據
        new_prices = {
            "gold": float(data['gold']),
            "silver": float(data['silver']),
            "copper": float(data['copper']),
            "aluminum": float(data['aluminum']),
            "lastUpdate": datetime.now().isoformat(),
            "updateTime": datetime.now().strftime('%Y年%m月%d日 %H:%M:%S'),
            "updatedBy": data.get('updatedBy', '管理員'),
            "source": "Flask API - 管理員更新"
        }

        # 保存價格數據
        if save_prices(new_prices):
            logger.info(f"管理員價格更新成功: {new_prices}")
            return jsonify({
                'success': True,
                'message': '價格更新成功',
                'data': new_prices
            }), 200
        else:
            return jsonify({'error': '價格保存失敗'}), 500

    except Exception as e:
        logger.error(f"管理員價格更新失敗: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/prices', methods=['GET'])
def api_get_prices():
    """API端點 - 獲取價格數據"""
    try:
        # 獲取緩存時間戳參數（用於前端緩存控制）
        cache_timestamp = request.args.get('t', '')
        logger.info(f"API請求 - 緩存時間戳: {cache_timestamp}")
        
        # 載入當前價格
        prices = load_prices()
        
        # 添加API相關信息
        prices['fetchedAt'] = datetime.now().isoformat()
        prices['fetchedBy'] = 'Flask API'
        prices['cacheTimestamp'] = cache_timestamp
        
        return jsonify(prices), 200
        
    except Exception as e:
        logger.error(f"API獲取價格失敗: {e}")
        
        # 返回錯誤時的預設價格
        error_prices = DEFAULT_PRICES.copy()
        error_prices.update({
            'fetchedAt': datetime.now().isoformat(),
            'fetchedBy': 'Flask API',
            'error': str(e),
            'note': 'API錯誤，返回預設價格'
        })
        
        return jsonify(error_prices), 200  # 仍返回200，但包含錯誤信息

@app.route('/api/prices', methods=['POST'])
def api_update_prices():
    """API端點 - 更新價格數據（JSON格式）"""
    try:
        # 獲取JSON數據
        data = request.get_json()
        
        if not data:
            return jsonify({'error': '無效的JSON數據'}), 400
        
        # 驗證必要字段
        required_fields = ['gold', 'silver', 'copper', 'aluminum']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'缺少必要字段: {field}'}), 400
            
            try:
                float(data[field])
            except (ValueError, TypeError):
                return jsonify({'error': f'字段 {field} 必須是有效數字'}), 400
        
        # 構建新的價格數據
        new_prices = {
            "gold": float(data['gold']),
            "silver": float(data['silver']),
            "copper": float(data['copper']),
            "aluminum": float(data['aluminum']),
            "lastUpdate": datetime.now().isoformat(),
            "updateTime": datetime.now().strftime('%Y年%m月%d日 %H:%M:%S'),
            "updatedBy": data.get('updatedBy', 'API用戶'),
            "source": "Flask API - JSON更新"
        }
        
        # 保存價格數據
        if save_prices(new_prices):
            logger.info(f"API價格更新成功: {new_prices}")
            return jsonify({
                'success': True,
                'message': '價格更新成功',
                'data': new_prices
            }), 200
        else:
            return jsonify({'error': '價格保存失敗'}), 500
            
    except Exception as e:
        logger.error(f"API更新價格失敗: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/health')
def health_check():
    """健康檢查端點"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'service': 'Price API'
    }), 200

def init_data_structure():
    """初始化數據結構"""
    # 創建數據目錄
    try:
        os.makedirs(DATA_DIR, exist_ok=True)
        print(f"✅ 數據目錄創建成功")
    except Exception:
        return False

    # 檢查價格文件是否存在
    if os.path.exists(PRICES_FILE):
        print(f"✅ 價格文件已存在")
        try:
            with open(PRICES_FILE, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return True
        except Exception as e:
            pass

    # 創建預設價格文件
    if save_prices(DEFAULT_PRICES):
        print(f"✅ 預設價格文件創建成功")
        return True
    else:
        return False

def check_permissions():
    """檢查文件權限"""
    try:
        test_file = os.path.join(DATA_DIR, "test_write.tmp")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        return True
    except Exception as e:
        return False

if __name__ == '__main__':
    # 初始化數據結構
    if not init_data_structure():
        sys.exit(1)

    # 檢查權限
    if not check_permissions():
        sys.exit(1)

    # 顯示首次生成的 token（如果有）
    if new_admin_token:
        print(f"🔑 管理員 token: {new_admin_token}")
        print("⚠️  請妥善保管上述 token，首次登入時需要使用")

    # 從環境變數獲取配置
    host = os.environ.get('FLASK_HOST', '0.0.0.0')
    port = int(os.environ.get('FLASK_PORT', 5000))
    debug = os.environ.get('FLASK_ENV') != 'production'

    # 啟動Flask應用
    app.run(debug=debug, host=host, port=port)
