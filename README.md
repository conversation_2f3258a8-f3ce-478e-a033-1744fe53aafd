# Flask 金屬價格管理 API

這是一個使用 Flask 構建的金屬價格管理系統，提供價格更新和查詢功能，支持表單驗證和 CORS。

## 🚀 功能特色

- **Web 管理介面**: 直觀的價格管理表單
- **表單驗證**: 完整的輸入驗證和錯誤處理
- **RESTful API**: 提供 JSON 格式的價格查詢和更新
- **CORS 支持**: 支持跨域請求
- **Unix 時間戳緩存**: 配合前端緩存機制
- **健康檢查**: 提供服務狀態監控

## 📦 安裝和運行

### 1. 安裝依賴

```bash
cd flask-api
pip install -r requirements.txt
```

### 2. 運行應用

```bash
python app.py
```

首次運行會自動：
- 創建數據目錄和初始價格文件
- 檢查文件權限
- 顯示系統信息

應用將在 `http://localhost:5000` 啟動。

### 3. 訪問管理介面

打開瀏覽器訪問：`http://localhost:5000`

## 🔌 API 端點

### 獲取價格數據

```http
GET /api/prices?t=1755669600
```

**響應示例：**
```json
{
  "gold": 2045.80,
  "silver": 24.50,
  "copper": 185.00,
  "aluminum": 45.00,
  "lastUpdate": "2024-01-20T10:30:00",
  "updateTime": "2024年01月20日 10:30:00",
  "updatedBy": "管理員",
  "source": "Flask API",
  "fetchedAt": "2024-01-20T10:30:00",
  "fetchedBy": "Flask API",
  "cacheTimestamp": "1755669600"
}
```

### 更新價格數據

```http
POST /api/prices
Content-Type: application/json

{
  "gold": 2050.00,
  "silver": 25.00,
  "copper": 190.00,
  "aluminum": 50.00,
  "updatedBy": "API用戶"
}
```

**響應示例：**
```json
{
  "success": true,
  "message": "價格更新成功",
  "data": {
    "gold": 2050.00,
    "silver": 25.00,
    "copper": 190.00,
    "aluminum": 50.00,
    "lastUpdate": "2024-01-20T10:35:00",
    "updateTime": "2024年01月20日 10:35:00",
    "updatedBy": "API用戶",
    "source": "Flask API - JSON更新"
  }
}
```

### 健康檢查

```http
GET /health
```

**響應示例：**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-20T10:30:00",
  "service": "Price API"
}
```

## 🔧 配置

### 環境變數

- `SECRET_KEY`: Flask 應用密鑰（生產環境必須更改）
- `FLASK_ENV`: 設置為 `production` 用於生產環境

### CORS 設置

默認允許的來源：
- `http://localhost:3000`
- `http://localhost:8000`
- `http://127.0.0.1:3000`
- `http://127.0.0.1:8000`

如需添加其他來源，請修改 `app.py` 中的 CORS 配置。

## 📁 文件結構

```
flask-api/
├── app.py                 # 主應用文件（包含所有功能）
├── requirements.txt       # Python 依賴
├── README.md             # 說明文件
├── data/                 # 數據目錄（自動創建）
│   └── prices.json       # 價格數據文件（自動創建）
├── templates/            # HTML 模板
│   └── price_admin.html  # 管理介面模板
└── static/              # 靜態文件（如需要）
```

## 🌍 跨平台支持

本 Flask API 支持在以下平台部署：

- **Windows** (Windows 10/11, Windows Server)
- **macOS** (macOS 10.14+)
- **Linux** (Ubuntu, CentOS, Debian, etc.)

### 路徑處理

應用使用智能路徑檢測，自動適配不同作業系統：

```python
# 自動檢測執行環境
if getattr(sys, 'frozen', False):
    # 打包成 exe 的情況
    executable_dir = os.path.dirname(sys.executable)
else:
    # 直接運行 .py 文件的情況
    executable_dir = os.path.dirname(os.path.abspath(__file__))

# 跨平台路徑構建
DATA_DIR = os.path.join(executable_dir, "data")
PRICES_FILE = os.path.join(DATA_DIR, "prices.json")
```

## 🔒 安全注意事項

1. **生產環境**: 請更改 `SECRET_KEY`
2. **CORS 設置**: 根據實際需求限制允許的來源
3. **輸入驗證**: 已實現基本的表單驗證
4. **錯誤處理**: 包含完整的錯誤處理機制

## 🧪 測試

### 測試 API 端點

```bash
# 獲取價格
curl http://localhost:5000/api/prices

# 更新價格
curl -X POST http://localhost:5000/api/prices \
  -H "Content-Type: application/json" \
  -d '{"gold": 2100, "silver": 26, "copper": 200, "aluminum": 55}'

# 健康檢查
curl http://localhost:5000/health
```

## 🔄 與靜態網站整合

這個 Flask API 設計用來與靜態網站配合使用：

1. 靜態網站通過 `js/price-reader.js` 調用 API
2. 使用 Unix 時間戳實現 15 分鐘緩存機制
3. 支持 CORS 跨域請求

## 📝 日誌

應用使用 Python 標準日誌模組，日誌級別設置為 INFO。所有重要操作都會記錄日誌。

## 🚀 部署建議

### 開發環境
```bash
# 初始化數據
python init_data.py

# 啟動開發服務器
python app.py
```

### 生產環境

#### Linux/macOS 部署
```bash
# 1. 安裝生產服務器
pip install gunicorn

# 2. 啟動服務（會自動初始化數據）
gunicorn -w 4 -b 0.0.0.0:5000 app:app
```

#### Windows 部署
```bash
# 1. 安裝 waitress（Windows 推薦）
pip install waitress

# 2. 啟動服務（會自動初始化數據）
waitress-serve --host=0.0.0.0 --port=5000 app:app
```

#### Docker 部署
```dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 5000
CMD ["python", "app.py"]
```

### VPS 部署注意事項

1. **數據持久化**: 確保 `data/` 目錄有寫入權限
2. **防火牆設置**: 開放 5000 端口（或自定義端口）
3. **反向代理**: 建議使用 Nginx 作為反向代理
4. **SSL 證書**: 生產環境建議使用 HTTPS
5. **環境變數**: 設置 `FLASK_ENV=production`
